#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的MCP服务器
实现记忆存储和调用功能
"""

import os
import json
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import hashlib
from pathlib import Path

# 配置参数
class Config:
    # 批处理大小
    BATCH_SIZE = 10
    # 最大文本长度
    MAX_TEXT_LENGTH = 10000
    # 是否启用去重
    ENABLE_DEDUPLICATION = True
    # 是否启用记忆存储
    ENABLE_MEMORY_STORAGE = True
    # 记忆存储路径
    MEMORY_STORAGE_PATH = "memory_storage"
    # 缓存路径
    CACHE_PATH = "cache"

# 初始化存储目录
def init_storage():
    """初始化存储目录"""
    os.makedirs(Config.MEMORY_STORAGE_PATH, exist_ok=True)
    os.makedirs(Config.CACHE_PATH, exist_ok=True)
    print(f"存储目录初始化完成: {Config.MEMORY_STORAGE_PATH}, {Config.CACHE_PATH}")

# 缓存管理
class CacheManager:
    """缓存管理器"""
    
    _cache: Dict[str, Any] = {}
    _stats = {"hits": 0, "misses": 0, "items": 0}
    
    @classmethod
    def get(cls, key: str) -> Optional[Any]:
        """获取缓存"""
        if key in cls._cache:
            cls._stats["hits"] += 1
            return cls._cache[key]
        cls._stats["misses"] += 1
        return None
    
    @classmethod
    def set(cls, key: str, value: Any) -> None:
        """设置缓存"""
        cls._cache[key] = value
        cls._stats["items"] = len(cls._cache)
    
    @classmethod
    def clear(cls) -> Dict[str, int]:
        """清理缓存"""
        old_stats = cls._stats.copy()
        cls._cache.clear()
        cls._stats = {"hits": 0, "misses": 0, "items": 0}
        return old_stats
    
    @classmethod
    def get_stats(cls) -> Dict[str, int]:
        """获取缓存统计信息"""
        return cls._stats.copy()

# 文本处理
def process_text_optimized(text: str) -> Dict[str, Any]:
    """处理单个文本，优化token使用
    
    Args:
        text: 要处理的文本
        
    Returns:
        处理结果和优化信息
    """
    if not text:
        return {"result": "", "info": "空文本"}
    
    # 文本长度限制
    if len(text) > Config.MAX_TEXT_LENGTH:
        text = text[:Config.MAX_TEXT_LENGTH]
        info = f"文本已截断至{Config.MAX_TEXT_LENGTH}字符"
    else:
        info = f"文本长度: {len(text)}字符"
    
    # 计算文本哈希用于缓存
    text_hash = hashlib.md5(text.encode()).hexdigest()
    
    # 检查缓存
    cached_result = CacheManager.get(text_hash)
    if cached_result:
        return {"result": cached_result, "info": f"{info} (缓存命中)"}
    
    # 实际处理逻辑
    # 这里可以添加更复杂的处理逻辑
    result = text
    
    # 存入缓存
    CacheManager.set(text_hash, result)
    
    return {"result": result, "info": info}

def process_batch_optimized(texts: List[str]) -> List[Dict[str, Any]]:
    """批量处理文本，减少请求次数
    
    Args:
        texts: 要处理的文本列表
        
    Returns:
        批量处理结果
    """
    results = []
    
    # 去重处理
    if Config.ENABLE_DEDUPLICATION:
        unique_texts = list(set(texts))
        if len(unique_texts) < len(texts):
            print(f"去重: {len(texts)} -> {len(unique_texts)}")
        texts = unique_texts
    
    # 批量处理
    for i in range(0, len(texts), Config.BATCH_SIZE):
        batch = texts[i:i+Config.BATCH_SIZE]
        batch_results = [process_text_optimized(text) for text in batch]
        results.extend(batch_results)
    
    return results

# 对话记忆管理
class ConversationMemory:
    """对话记忆管理"""
    
    @staticmethod
    def _get_session_path(session_id: str) -> str:
        """获取会话文件路径"""
        return os.path.join(Config.MEMORY_STORAGE_PATH, f"{session_id}.json")
    
    @classmethod
    def save(cls, session_id: str, user_message: str, assistant_response: str) -> Dict[str, Any]:
        """保存对话到记忆系统
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            assistant_response: 助手回复
            
        Returns:
            保存结果
        """
        if not Config.ENABLE_MEMORY_STORAGE:
            return {"status": "disabled", "message": "记忆存储已禁用"}
        
        # 创建对话记录
        conversation = {
            "timestamp": datetime.now().isoformat(),
            "user_message": user_message,
            "assistant_response": assistant_response
        }
        
        # 获取现有记忆
        session_path = cls._get_session_path(session_id)
        if os.path.exists(session_path):
            try:
                with open(session_path, 'r', encoding='utf-8') as f:
                    memory = json.load(f)
            except Exception as e:
                memory = {"conversations": []}
                print(f"读取记忆文件失败: {e}")
        else:
            memory = {"conversations": []}
        
        # 添加新对话
        memory["conversations"].append(conversation)
        memory["last_updated"] = datetime.now().isoformat()
        
        # 保存记忆
        try:
            with open(session_path, 'w', encoding='utf-8') as f:
                json.dump(memory, f, ensure_ascii=False, indent=2)
            return {
                "status": "success", 
                "message": f"对话已保存到会话 {session_id}",
                "total_conversations": len(memory["conversations"])
            }
        except Exception as e:
            return {"status": "error", "message": f"保存记忆失败: {str(e)}"}
    
    @classmethod
    def get(cls, session_id: str, limit: Optional[int] = None) -> Dict[str, Any]:
        """获取对话记忆
        
        Args:
            session_id: 会话ID
            limit: 限制返回的对话数量
            
        Returns:
            对话历史
        """
        if not Config.ENABLE_MEMORY_STORAGE:
            return {"status": "disabled", "message": "记忆存储已禁用", "conversations": []}
        
        session_path = cls._get_session_path(session_id)
        if not os.path.exists(session_path):
            return {
                "status": "not_found", 
                "message": f"未找到会话 {session_id} 的记忆",
                "conversations": []
            }
        
        try:
            with open(session_path, 'r', encoding='utf-8') as f:
                memory = json.load(f)
            
            conversations = memory.get("conversations", [])
            
            # 限制返回数量
            if limit and limit > 0:
                conversations = conversations[-limit:]
            
            return {
                "status": "success",
                "message": f"成功获取会话 {session_id} 的记忆",
                "total": len(memory.get("conversations", [])),
                "returned": len(conversations),
                "conversations": conversations
            }
        except Exception as e:
            return {
                "status": "error", 
                "message": f"读取记忆失败: {str(e)}",
                "conversations": []
            }

# 会话状态管理
class ConversationStatus:
    """会话状态管理"""
    
    _status: Dict[str, Dict[str, Any]] = {}
    
    @classmethod
    def update(cls, session_id: str, status: Dict[str, Any]) -> None:
        """更新会话状态"""
        if session_id not in cls._status:
            cls._status[session_id] = {}
        cls._status[session_id].update(status)
        cls._status[session_id]["last_updated"] = datetime.now().isoformat()
    
    @classmethod
    def get(cls, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        if session_id not in cls._status:
            return {"status": "not_found", "message": f"未找到会话 {session_id} 的状态"}
        return cls._status[session_id]

# MCP核心功能
class MCP:
    """MCP核心功能"""
    
    @staticmethod
    def ask_question(session_id: str, question: str, context_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """提问时调用MCP，自动保存上下文
        
        Args:
            session_id: 会话ID
            question: 提问内容
            context_info: 上下文信息（可选）
            
        Returns:
            提问结果
        """
        # 处理问题
        processed = process_text_optimized(question)
        
        # 更新会话状态
        ConversationStatus.update(session_id, {
            "last_question": question,
            "context_info": context_info,
            "waiting_for_response": True
        })
        
        return {
            "status": "success",
            "message": "问题已处理",
            "processed": processed,
            "session_id": session_id
        }
    
    @staticmethod
    def complete_request(
        session_id: str, 
        user_message: str, 
        assistant_response: str,
        final: bool = False
    ) -> Dict[str, Any]:
        """完成用户请求时调用MCP，而不是简单结束进程
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            assistant_response: 助手回复
            final: 是否为最终回复（无需等待进一步反馈）
            
        Returns:
            完成状态
        """
        # 保存对话记忆
        memory_result = ConversationMemory.save(session_id, user_message, assistant_response)
        
        # 更新会话状态
        ConversationStatus.update(session_id, {
            "last_user_message": user_message,
            "last_assistant_response": assistant_response,
            "waiting_for_feedback": not final,
            "waiting_for_response": False,
            "completed": final
        })
        
        return {
            "status": "success",
            "message": "请求已完成" if final else "等待用户反馈",
            "memory_saved": memory_result,
            "session_id": session_id
        }
    
    @staticmethod
    def process_feedback(session_id: str, user_feedback: str) -> Dict[str, Any]:
        """处理用户反馈，持续调用MCP直到用户反馈为空
        
        Args:
            session_id: 会话ID
            user_feedback: 用户反馈内容
            
        Returns:
            反馈处理结果
        """
        # 检查反馈是否为空
        if not user_feedback.strip():
            # 更新会话状态为已完成
            ConversationStatus.update(session_id, {
                "waiting_for_feedback": False,
                "completed": True
            })
            
            return {
                "status": "completed",
                "message": "用户反馈为空，请求已完成",
                "session_id": session_id
            }
        
        # 处理非空反馈
        processed = process_text_optimized(user_feedback)
        
        # 更新会话状态
        ConversationStatus.update(session_id, {
            "last_feedback": user_feedback,
            "waiting_for_response": True,
            "waiting_for_feedback": False
        })
        
        return {
            "status": "success",
            "message": "反馈已处理，等待回复",
            "processed": processed,
            "session_id": session_id
        }
    
    @staticmethod
    def auto_mcp_call(
        session_id: str,
        user_message: str,
        assistant_response: str,
        action: str = "complete",
        is_final: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """自动MCP调用工具 - 实现"Whenever you want to ask a question, always call the MCP"等功能
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            assistant_response: 助手回复
            action: 动作类型 ("ask", "complete", "process_feedback")
            is_final: 是否为最终回复
            metadata: 额外元数据
            
        Returns:
            处理结果
        """
        if action == "ask":
            return MCP.ask_question(session_id, user_message, metadata)
        elif action == "complete":
            return MCP.complete_request(session_id, user_message, assistant_response, is_final)
        elif action == "process_feedback":
            return MCP.process_feedback(session_id, user_message)
        else:
            return {
                "status": "error",
                "message": f"未知动作类型: {action}",
                "session_id": session_id
            }

# 配置优化
def optimize_config(
    batch_size: Optional[int] = None,
    max_text_length: Optional[int] = None,
    enable_deduplication: Optional[bool] = None,
    enable_memory_storage: Optional[bool] = None
) -> Dict[str, Any]:
    """动���调整优化配置
    
    Args:
        batch_size: 批处理大小
        max_text_length: 最大文本长度
        enable_deduplication: 启用去重
        enable_memory_storage: 启用记忆存储
        
    Returns:
        配置更新结果
    """
    old_config = {
        "batch_size": Config.BATCH_SIZE,
        "max_text_length": Config.MAX_TEXT_LENGTH,
        "enable_deduplication": Config.ENABLE_DEDUPLICATION,
        "enable_memory_storage": Config.ENABLE_MEMORY_STORAGE
    }
    
    # 更新配置
    if batch_size is not None:
        Config.BATCH_SIZE = batch_size
    
    if max_text_length is not None:
        Config.MAX_TEXT_LENGTH = max_text_length
    
    if enable_deduplication is not None:
        Config.ENABLE_DEDUPLICATION = enable_deduplication
    
    if enable_memory_storage is not None:
        Config.ENABLE_MEMORY_STORAGE = enable_memory_storage
        if enable_memory_storage:
            os.makedirs(Config.MEMORY_STORAGE_PATH, exist_ok=True)
    
    new_config = {
        "batch_size": Config.BATCH_SIZE,
        "max_text_length": Config.MAX_TEXT_LENGTH,
        "enable_deduplication": Config.ENABLE_DEDUPLICATION,
        "enable_memory_storage": Config.ENABLE_MEMORY_STORAGE
    }
    
    return {
        "status": "success",
        "message": "配置已更新",
        "old_config": old_config,
        "new_config": new_config
    }

# 工具函数
def list_memory_sessions() -> Dict[str, Any]:
    """列出所有记忆会话"""
    if not Config.ENABLE_MEMORY_STORAGE:
        return {"status": "disabled", "message": "记忆存储已禁用", "sessions": []}
    
    try:
        if not os.path.exists(Config.MEMORY_STORAGE_PATH):
            return {"status": "not_found", "message": "记忆存储目录不存在", "sessions": []}
        
        sessions = []
        for file in os.listdir(Config.MEMORY_STORAGE_PATH):
            if file.endswith('.json'):
                session_id = file[:-5]  # 移除 .json 后缀
                session_path = os.path.join(Config.MEMORY_STORAGE_PATH, file)
                
                try:
                    with open(session_path, 'r', encoding='utf-8') as f:
                        memory = json.load(f)
                    
                    sessions.append({
                        "session_id": session_id,
                        "last_updated": memory.get("last_updated", "未知"),
                        "conversations": len(memory.get("conversations", []))
                    })
                except Exception as e:
                    sessions.append({
                        "session_id": session_id,
                        "error": f"读取失败: {str(e)}"
                    })
        
        return {
            "status": "success",
            "message": f"找到 {len(sessions)} 个会话",
            "sessions": sessions
        }
    except Exception as e:
        return {"status": "error", "message": f"列出会话失败: {str(e)}", "sessions": []}

def delete_memory_session(session_id: str) -> Dict[str, Any]:
    """删除指定的记忆会话"""
    if not Config.ENABLE_MEMORY_STORAGE:
        return {"status": "disabled", "message": "记忆存储已禁用"}
    
    session_path = os.path.join(Config.MEMORY_STORAGE_PATH, f"{session_id}.json")
    if not os.path.exists(session_path):
        return {"status": "not_found", "message": f"未找到会话 {session_id}"}
    
    try:
        os.remove(session_path)
        # 同时清除会话状态
        if session_id in ConversationStatus._status:
            del ConversationStatus._status[session_id]
        return {"status": "success", "message": f"已删除会话 {session_id}"}
    except Exception as e:
        return {"status": "error", "message": f"删除会话失败: {str(e)}"}

# 主函数
def main():
    """主函数"""
    print("初始化MCP服务器...")
    init_storage()
    print("MCP服务器已启动")
    print(f"记忆存储路径: {Config.MEMORY_STORAGE_PATH}")
    print(f"缓存路径: {Config.CACHE_PATH}")
    
    # 列出现有会话
    sessions = list_memory_sessions()
    if sessions["status"] == "success" and sessions["sessions"]:
        print(f"找到 {len(sessions['sessions'])} 个现有会话:")
        for session in sessions["sessions"]:
            print(f"  - {session['session_id']}: {session.get('conversations', '未知')} 条对话")
    else:
        print("未找到现有会话")
    
    print("\n使用示例:")
    print("1. 提问: MCP.ask_question('session1', '你好，请问如何使用MCP?')")
    print("2. 完成请求: MCP.complete_request('session1', '用户问题', '助手回复')")
    print("3. 处理反馈: MCP.process_feedback('session1', '用户反馈')")
    print("4. 自动调用: MCP.auto_mcp_call('session1', '用户消息', '助手回复', 'complete')")
    print("5. 查看记忆: ConversationMemory.get('session1')")
    print("6. 列出会话: list_memory_sessions()")
    print("7. ��除会话: delete_memory_session('session1')")
    print("8. 优化配置: optimize_config(batch_size=20)")

if __name__ == "__main__":
    main()