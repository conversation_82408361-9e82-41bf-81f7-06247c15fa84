# 优化的MCP服务器 - FastMCP版本

这是一个使用 FastMCP 框架改造的优化MCP服务器，提供了完整的对话记忆存储、文本处理和会话管理功能。

## 功能特性

### 核心功能
- **文本处理**: 单个和批量文本处理，支持缓存优化
- **对话记忆**: 自动保存和检索对话历史
- **会话管理**: 跟踪会话状态和上下文
- **缓存系统**: 智能缓存减少重复处理
- **配置优化**: 动态调整服务器配置

### 可用工具

#### 文本处理工具
- `process_text_optimized`: 处理单个文本，支持长度限制和缓存
- `process_batch_optimized`: 批量处理文本，支持去重和分批处理

#### 记忆管理工具
- `save_conversation_memory`: 保存对话到记忆系统
- `get_conversation_memory`: 获取对话历史记录
- `list_memory_sessions`: 列出所有记忆会话
- `delete_memory_session`: 删除指定会话

#### MCP核心工具
- `ask_question`: 提问时调用MCP，自动保存上下文
- `complete_request`: 完成用户请求，支持最终标记
- `process_feedback`: 处理用户反馈，持续交互
- `auto_mcp_call`: 自动MCP调用，支持多种动作类型

#### 系统管理工具
- `get_session_status`: 获取会话状态信息
- `get_cache_stats`: 获取缓存统计信息
- `clear_cache`: 清理缓存
- `optimize_config`: 动态调整配置参数
- `init_storage_directories`: 初始化存储目录

## 安装和运行

### 前置要求
```bash
pip install fastmcp
```

### 启动服务器
```bash
# 直接运行
python optimized_server.py

# 或使用 FastMCP CLI
fastmcp run optimized_server.py
```

### 测试服务器
```bash
python test_mcp_client.py
```

## 使用示例

### 基本文本处理
```python
from fastmcp import Client
import asyncio

async def example():
    client = Client("optimized_server.py")
    async with client:
        # 处理文本
        result = await client.call_tool("process_text_optimized", {
            "text": "这是要处理的文本"
        })
        print(result[0].text)
```

### 对话记忆管理
```python
async def memory_example():
    client = Client("optimized_server.py")
    async with client:
        # 保存对话
        await client.call_tool("save_conversation_memory", {
            "session_id": "user123",
            "user_message": "你好",
            "assistant_response": "你好！有什么可以帮助您的吗？"
        })
        
        # 获取对话历史
        history = await client.call_tool("get_conversation_memory", {
            "session_id": "user123"
        })
        print(history[0].text)
```

### MCP工作流程
```python
async def mcp_workflow():
    client = Client("optimized_server.py")
    async with client:
        session_id = "workflow_session"
        
        # 1. 提问
        await client.call_tool("ask_question", {
            "session_id": session_id,
            "question": "如何使用这个服务？"
        })
        
        # 2. 完成请求
        await client.call_tool("complete_request", {
            "session_id": session_id,
            "user_message": "如何使用这个服务？",
            "assistant_response": "这是一个MCP服务器，提供多种工具...",
            "final": True
        })
        
        # 3. 检查状态
        status = await client.call_tool("get_session_status", {
            "session_id": session_id
        })
        print(status[0].text)
```

## 配置选项

### 默认配置
- `BATCH_SIZE`: 10 (批处理大小)
- `MAX_TEXT_LENGTH`: 10000 (最大文本长度)
- `ENABLE_DEDUPLICATION`: True (启用去重)
- `ENABLE_MEMORY_STORAGE`: True (启用记忆存储)

### 动态配置调整
```python
await client.call_tool("optimize_config", {
    "batch_size": 20,
    "max_text_length": 5000,
    "enable_deduplication": False
})
```

## 存储结构

### 目录结构
```
memory_storage/          # 记忆存储目录
├── session1.json       # 会话1的对话记录
├── session2.json       # 会话2的对话记录
└── ...

cache/                   # 缓存目录
```

### 记忆文件格式
```json
{
  "conversations": [
    {
      "timestamp": "2025-06-01T21:11:56.133946",
      "user_message": "用户消息",
      "assistant_response": "助手回复"
    }
  ],
  "last_updated": "2025-06-01T21:11:56.145409"
}
```

## 性能优化

1. **缓存机制**: 自动缓存处理结果，避免重复计算
2. **批量处理**: 支持批量文本处理，提高效率
3. **去重功能**: 自动去除重复文本，减少处理量
4. **长度限制**: 自动截断过长文本，控制资源使用

## 错误处理

服务器提供完善的错误处理机制：
- 文件读写错误自动恢复
- 配置参数验证
- 会话状态一致性检查
- 缓存异常处理

## 扩展开发

基于 FastMCP 框架，可以轻松添加新的工具：

```python
@mcp.tool()
def my_custom_tool(param1: str, param2: int) -> dict:
    """自定义工具描述"""
    # 实现逻辑
    return {"result": "success"}
```

## 许可证

本项目基于原有的优化MCP服务器改造，使用 FastMCP 框架实现。
